/* USER CODE BEGIN Header */
/**
  * @file           : main.c
  * @brief          : Main program body
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "spi.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

//更新日期：2025-8-20
//修复内容：
//1.数据发送时打包发送历史数据，提升发送效率；（一次打包最多8条，单轮发送最多100条）；
//2.工作时间段设置问题，当前代码实现工作时间段智能判断，非工作时间段不发送数据；
//3.修复RTC不准确的问题（休眠时间补偿）；

//更新日期：2025-8-26
//1.修复UTC日期零时跨天发生跳转错误的问题；

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "globals.h"
#include "FLASH/bsp_flash.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "network_command.h"
#include "GPS.h"
#include "rtc_sync.h"
#include "GSM.h"
#include "network_command.h"

#define UART1_RX_BUFFER_SIZE 1
uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE];
#define GPS_BUFFER_SIZE 1024  // 增加缓冲区大小，避免溢出
char gps_buffer[GPS_BUFFER_SIZE];
uint16_t gps_buffer_index = 0;

// LPUART1 (GSM/网络指令) 接收缓冲区
#define LPUART1_RX_BUFFER_SIZE 1
uint8_t lpuart1_rx_buffer[LPUART1_RX_BUFFER_SIZE];

// 网络指令缓冲区
#define NETWORK_CMD_BUFFER_SIZE 32
#define NETWORK_CMD_QUEUE_SIZE 4  // 最多缓存4条指令
char network_cmd_queue[NETWORK_CMD_QUEUE_SIZE][NETWORK_CMD_BUFFER_SIZE];  // 指令队列
uint8_t network_cmd_queue_head = 0;  // 队列头（写入位置）
uint8_t network_cmd_queue_tail = 0;  // 队列尾（读取位置）
uint8_t network_cmd_queue_count = 0; // 队列中指令数量

// GSM中断相关变量已删除，改用阻塞式通信
// 保留少量变量以兼容编译
#define GSM_INTERRUPT_BUFFER_SIZE 512
char gsm_interrupt_buffer[GSM_INTERRUPT_BUFFER_SIZE];
uint16_t gsm_interrupt_buffer_index = 0;

#define IS_GPS_PWR_ON() (HAL_GPIO_ReadPin(GPS_PWR_GPIO_Port, GPS_PWR_Pin) == GPIO_PIN_SET)

uint8_t int_wake_src = 0;

// 网络指令队列操作函数
uint8_t NetworkCmd_QueuePush(const char* cmd) {
  if (network_cmd_queue_count >= NETWORK_CMD_QUEUE_SIZE) {
    return 0; // 队列满
  }

  strncpy(network_cmd_queue[network_cmd_queue_head], cmd, NETWORK_CMD_BUFFER_SIZE - 1);
  network_cmd_queue[network_cmd_queue_head][NETWORK_CMD_BUFFER_SIZE - 1] = '\0';

  network_cmd_queue_head = (network_cmd_queue_head + 1) % NETWORK_CMD_QUEUE_SIZE;
  network_cmd_queue_count++;

  return 1; // 成功
}

uint8_t NetworkCmd_QueuePop(char* cmd) {
  if (network_cmd_queue_count == 0) {
    return 0; // 队列空
  }

  strncpy(cmd, network_cmd_queue[network_cmd_queue_tail], NETWORK_CMD_BUFFER_SIZE);

  network_cmd_queue_tail = (network_cmd_queue_tail + 1) % NETWORK_CMD_QUEUE_SIZE;
  network_cmd_queue_count--;

  return 1; // 成功
}

LSM6DS3_Data imuData;
LSM6DS3_Attitude attitude;

void PrintCurrentTime(void);
uint8_t Check_Button_Status(void);
void Enter_Sleep_Mode(uint32_t sleep_seconds);
void UART2_SendString(const char *str);
void UART2_SendStatus(uint8_t status_code, const char *short_msg);
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
void Print_Data_String(const char *data_string); // 替代GM20发送的打印函数
void GPS_ParseData(void);
void Test_GPS_KeyParameters(void); // 新增：测试GPS关键参数打印功能

HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value);
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *value);
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// Print current RTC time
void PrintCurrentTime(void) {
  RTC_TimeTypeDef time;
  RTC_DateTypeDef date;
  RTC_GetDateTime(&time, &date);
}

// External variables
extern GPS_Data_t gps_data;
extern float pw;

// GM20相关函数已移除，用于测试除GSM外的其他功能

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_I2C1_Init();
  MX_RTC_Init();
  MX_SPI1_Init();
  MX_LPUART1_UART_Init();
  /* USER CODE BEGIN 2 */
  // Initialize UART receive buffers
  memset(uart1_rx_buffer, 0, UART1_RX_BUFFER_SIZE);
  memset(lpuart1_rx_buffer, 0, LPUART1_RX_BUFFER_SIZE);
  gps_buffer_index = 0;
  // GSM中断变量初始化已删除

  // Start UART receive interrupts
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);  // GPS
  // LPUART1改用阻塞模式，不需要中断接收

  // Enable backup domain access
  HAL_PWR_EnableBkUpAccess();

  // Load RTC settings and initialize sync mechanism
  RTC_LoadSettings();
  RTC_Sync_Init();
  PrintCurrentTime();

	LED1_TOGGLE;
	HAL_Delay(300);
	LED1_TOGGLE;
	HAL_Delay(300);
  LED1_TOGGLE;
	HAL_Delay(300);
  LED1_TOGGLE;
	HAL_Delay(300);
	LED1_OFF;

  V_OUT_ON;
  HAL_Delay(500);  // Wait for power stabilization

  // Initialize SPI Flash
  if (SPI_FLASH_Init() == HAL_OK) {
    printf("SPI Flash OK\r\n");
    // Print unread data count
    unsigned long unread_count = SPI_FLASH_GetRecordCount();
    printf("Flash data: %lu unread\r\n", unread_count);

    // 手动设置N指令 - 方便测试时清除N指令
    // 取消注释下面的代码来清除N指令设置
    extern uint32_t pack_count_threshold;
    extern uint8_t pack_count_enabled;
    pack_count_threshold = 0;     // 清除N指令阈值
    pack_count_enabled = 0;       // 禁用N指令

    // 立即保存到Flash，覆盖之前的N指令设置
    if (Flash_WriteUint32(3, 0) == HAL_OK) {  // FLASH_INDEX_PACK_COUNT = 3
      printf("Manual N command cleared and saved to Flash: threshold=0, enabled=0\r\n");
    } else {
      printf("Failed to save N command clear to Flash\r\n");
    }


  //手动读取历史数据（测试用，不标记为已读）
//  SPI_FLASH_ReadMultipleRecords(200); // 测试读取前10条数据

			// 测试前清空Flash
      printf("Erasing entire chip...\r\n");
      if (SPI_FLASH_EraseChip() == HAL_OK) {
        printf("Chip erase: OK\r\n");
      } else {
        printf("Chip erase: FAIL\r\n");
      }

      // 手动重置环形缓冲区地址到起始位置
      printf("Resetting ring buffer to start position...\r\n");
      if (SPI_FLASH_ClearRingBuffer() == HAL_OK) {
        printf("Ring buffer reset: OK\r\n");
      } else {
        printf("Ring buffer reset: FAIL\r\n");
      }
      printf("After reset - Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());



  // 手动设置工作时间段到FLASH（解决设备长久休眠问题）
  #if 1  // 设置为1启用，设置为0禁用
  {
    printf("=== Manual Work Time Setup ===\r\n");

    // 设置工作时间段为 00:00 - 23:59（全天工作，避免长久休眠）
    NetworkCommand_Result_t test_work_time;
    test_work_time.type = CMD_TYPE_WORK_TIME;
    test_work_time.value1 = 0;   // 开始时间：0点
    test_work_time.value2 = 0;  // 结束时间：23点
    test_work_time.is_valid = 1;

    if (NetworkCommand_SaveToFlash(&test_work_time) == HAL_OK) {
      printf("Manual work time saved: %02lu:00 - %02lu:00 (Full day active)\r\n",
             test_work_time.value1, test_work_time.value2);
    } else {
      printf("Failed to save manual work time\r\n");
    }

    printf("=== End Manual Work Time Setup ===\r\n");
  }
  #endif


    // 手动写入测试数据 测试历史数据发送
    #if 0  // 设置为1启用测试，设置为0禁用测试
    {
      // 测试数量宏定义
      #define TEST_WRITE_COUNT 100  // 写入测试数据 测试历史数据发送

      printf("=== 1 SPI Flash Manual Read/Write 100 ===\r\n");

//      // 测试前清空Flash
      printf("Erasing entire chip...\r\n");
      if (SPI_FLASH_EraseChip() == HAL_OK) {
        printf("Chip erase: OK\r\n");
      } else {
        printf("Chip erase: FAIL\r\n");
      }

      // 手动重置环形缓冲区地址到起始位置
      printf("Resetting ring buffer to start position...\r\n");
      if (SPI_FLASH_ClearRingBuffer() == HAL_OK) {
        printf("Ring buffer reset: OK\r\n");
      } else {
        printf("Ring buffer reset: FAIL\r\n");
      }
      printf("After reset - Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());

      // 写入测试数据
      for (int i = 1; i <= TEST_WRITE_COUNT; i++) {
        char test_data[SPI_FLASH_RECORD_SIZE];
        snprintf(test_data, sizeof(test_data),
                 "HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+12345312278524090%05d+E", i);

        uint8_t writeData[SPI_FLASH_RECORD_SIZE];
        memset(writeData, 0, SPI_FLASH_RECORD_SIZE);
        strcpy((char *)writeData, test_data);

        if (SPI_FLASH_WriteRecord(writeData, SPI_FLASH_RECORD_SIZE) == HAL_OK) {
            // 每100条打印一次进度
            if (i % 100 == 0 || i == TEST_WRITE_COUNT) {
                printf("Write progress: %d/%d\r\n", i, TEST_WRITE_COUNT);
            }
        } else {
            printf("Test write %d: FAIL\r\n", i);
            break; // 写入失败时停止
        }
        HAL_Delay(1); // 减少延时，加快测试速度
      }

      printf("Write test completed. Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());
      // 取消宏定义
      #undef TEST_WRITE_COUNT
      #undef TEST_READ_COUNT
    }
    #endif





    // 环形缓冲区回绕测试——针对回环发生情况测试
    #if 0  // 设置为1启用回绕测试，设置为0禁用测试
    {
        printf("\r\n=== Ring Buffer Wrap Test ===\r\n");

        // 测试参数配置（可手动修改）
        #define WRAP_TEST_OFFSET 100    // 距离最大容量的偏移量
        #define WRAP_TEST_COUNT  200    // 测试写入数量

        // 自动计算目标位置
        uint32_t max_capacity = SPI_FLASH_MAX_RECORDS;
        uint32_t target_records = max_capacity - WRAP_TEST_OFFSET;

        printf("Max: %lu, Target: %lu (-%d), Test: %d\r\n",
               max_capacity, target_records, WRAP_TEST_OFFSET, WRAP_TEST_COUNT);

        // 清空Flash并重置
        if (SPI_FLASH_EraseChip() == HAL_OK && SPI_FLASH_ClearRingBuffer() == HAL_OK) {

            // 手动设置环形缓冲区到目标位置
            extern SPI_FLASH_RingBuffer_t RingBuffer;
            RingBuffer.TotalRecords = target_records;
            RingBuffer.ReadRecords = target_records;
            RingBuffer.CurrentAddr = SPI_FLASH_RING_BUFFER_START + ((target_records % max_capacity) * SPI_FLASH_RECORD_SIZE);

            printf("Set to: Records=%lu, Addr=0x%08X\r\n",
                   RingBuffer.TotalRecords, RingBuffer.CurrentAddr);

            // 保存状态到内部Flash
            SPI_FLASH_SaveRingBufferInfo();

            // 开始写入测试数据
            printf("Writing %d records...\r\n", WRAP_TEST_COUNT);
            uint32_t wrap_detected = 0;
            uint32_t first_addr = RingBuffer.CurrentAddr;

            for (int i = 1; i <= WRAP_TEST_COUNT; i++) {
                char test_data[SPI_FLASH_RECORD_SIZE];
                snprintf(test_data, sizeof(test_data),
                        "HY109S+119.96216+30.27593+063652.110725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+89852312278524090%05d+E",
                        i);

                uint8_t writeData[SPI_FLASH_RECORD_SIZE];
                memset(writeData, 0, SPI_FLASH_RECORD_SIZE);
                strcpy((char *)writeData, test_data);

                uint32_t addr_before = RingBuffer.CurrentAddr;

                if (SPI_FLASH_WriteRecord(writeData, SPI_FLASH_RECORD_SIZE) == HAL_OK) {
                    // 检测地址回绕
                    if (!wrap_detected && RingBuffer.CurrentAddr < addr_before) {
                        printf("WRAP at record %d: 0x%08X -> 0x%08X\r\n",
                               i, addr_before, RingBuffer.CurrentAddr);
                        wrap_detected = 1;
                    }

                    // 每100条打印进度
                    if (i % 100 == 0 || i == WRAP_TEST_COUNT) {
                        printf("Progress: %d/%d\r\n", i, WRAP_TEST_COUNT);
                    }
                } else {
                    printf("Write FAIL at %d\r\n", i);
                    break;
                }
            }

            printf("Write done. First: 0x%08X, Final: 0x%08X\r\n",
                   first_addr, RingBuffer.CurrentAddr);

            // 开始读取测试
            printf("Reading %d records...\r\n", WRAP_TEST_COUNT);
            uint32_t read_count = SPI_FLASH_GetRecordCount();
            printf("Unread: %lu\r\n", read_count);

            uint32_t max_read = (read_count > WRAP_TEST_COUNT) ? WRAP_TEST_COUNT : read_count;
            uint32_t correct_count = 0;
            uint32_t error_count = 0;

            for (uint32_t i = 0; i < max_read; i++) {
                uint8_t readData[SPI_FLASH_RECORD_SIZE];
                uint16_t data_size = SPI_FLASH_RECORD_SIZE;

                if (SPI_FLASH_ReadRecordEx(i, readData, &data_size, 0) == HAL_OK) {  // 测试读取不同索引，不标记为已读
                    // 验证数据正确性
                    char expected_suffix[32];
                    snprintf(expected_suffix, sizeof(expected_suffix), "90%05d+E", (int)(i + 1));

                    if (strstr((char*)readData, expected_suffix) != NULL) {
                        correct_count++;
                        printf("Read %lu: %s\r\n", i + 1, (char*)readData);
                    } else {
                        error_count++;
                        printf("Read %lu: ERROR - %s\r\n", i + 1, (char*)readData);
                        printf("  Expected: ...%s\r\n", expected_suffix);
                    }
                } else {
                    printf("Read %lu: FAIL\r\n", i + 1);
                    error_count++;
                }
            }

            // 测试结果统计
            printf("Results: %lu/%lu correct (%.1f%%)\r\n",
                   correct_count, max_read, (float)correct_count / max_read * 100.0f);

            if (error_count == 0) {
                printf("WRAP TEST PASSED!\r\n");
            } else {
                printf("WRAP TEST FAILED - %lu errors!\r\n", error_count);
            }

        } else {
            printf("Flash clear FAILED\r\n");
        }

        printf("=== Wrap Test Done ===\r\n\r\n");
    }
    #endif



////////////////////////////////////////////////////////////////////////////

    // SPI Flash手动读写测试——大量写入极限测试
    #if 0  // 设置为1启用测试，设置为0禁用测试
    {
      // 测试数量宏定义
      #define TEST_WRITE_COUNT 1000  // 写入测试数量比实际容量大100条 测试覆盖读取
      #define TEST_READ_COUNT  1000  // 读取测试数量

      printf("=== 1 SPI Flash Manual Read/Write 1000 ===\r\n");

      // 测试前清空Flash
      printf("Erasing entire chip...\r\n");
      if (SPI_FLASH_EraseChip() == HAL_OK) {
        printf("Chip erase: OK\r\n");
      } else {
        printf("Chip erase: FAIL\r\n");
      }

      // 手动重置环形缓冲区地址到起始位置
      printf("Resetting ring buffer to start position...\r\n");
      if (SPI_FLASH_ClearRingBuffer() == HAL_OK) {
        printf("Ring buffer reset: OK\r\n");
      } else {
        printf("Ring buffer reset: FAIL\r\n");
      }
      printf("After reset - Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());

      // 写入测试数据
      for (int i = 1; i <= TEST_WRITE_COUNT; i++) {
        char test_data[SPI_FLASH_RECORD_SIZE];
        snprintf(test_data, sizeof(test_data),
                 "HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+89852312278524090%05d+E", i);

        uint8_t writeData[SPI_FLASH_RECORD_SIZE];
        memset(writeData, 0, SPI_FLASH_RECORD_SIZE);
        strcpy((char *)writeData, test_data);

        if (SPI_FLASH_WriteRecord(writeData, SPI_FLASH_RECORD_SIZE) == HAL_OK) {
            // 每100条打印一次进度
            if (i % 100 == 0 || i == TEST_WRITE_COUNT) {
                printf("Write progress: %d/%d\r\n", i, TEST_WRITE_COUNT);
            }
        } else {
            printf("Test write %d: FAIL\r\n", i);
            break; // 写入失败时停止
        }
        HAL_Delay(1); // 减少延时，加快测试速度
      }

      printf("Write test completed. Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());

      // 打印环形缓冲区详细状态
      SPI_FLASH_PrintRingBufferStatus();

      // 读取测试数据
      printf("--- Reading test data ---\r\n");
      uint32_t read_count = SPI_FLASH_GetRecordCount();
      printf("Total unread records: %lu\r\n", read_count);

      // 读取测试数据
      uint32_t max_read = (read_count > TEST_READ_COUNT) ? TEST_READ_COUNT : read_count;
      for (uint32_t i = 0; i < max_read; i++) {
        uint8_t readData[SPI_FLASH_RECORD_SIZE];
        uint16_t data_size = SPI_FLASH_RECORD_SIZE;

        if (SPI_FLASH_ReadRecordEx(i, readData, &data_size, 0) == HAL_OK) {  // 测试读取不同索引，不标记为已读
          printf("Test read %lu: %s\r\n", i + 1, (char*)readData);
        } else {
          printf("Test read %lu: FAIL\r\n", i + 1);
          break;
        }
        HAL_Delay(1); // 减少延时，加快测试速度
      }

      printf("Read test completed: %lu records processed\r\n", max_read);

      printf("=== Test Completed ===\r\n");

      // 取消宏定义
      #undef TEST_WRITE_COUNT
      #undef TEST_READ_COUNT
    }
    #endif
  }

  /* USER CODE END 2 */

  /* Call init function for freertos objects (in cmsis_os2.c) */
  MX_FREERTOS_Init();

  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
// Printf redirection to UART
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);  // huart1 RX连接GPS TX 定向为打印输出
    return ch;
}

// UART receive complete callback
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if(huart->Instance == USART1)  // GPS module
  {
    if (gps_buffer_index < GPS_BUFFER_SIZE - 1) {
      gps_buffer[gps_buffer_index++] = uart1_rx_buffer[0];
      gps_buffer[gps_buffer_index] = '\0';

      // Check for complete NMEA sentence
      if (uart1_rx_buffer[0] == '\n') {
        gps_new_data = 1;
      }
    } else {
      // Buffer full, reset
//      printf("GPS: Buffer full, resetting\r\n");
      gps_buffer_index = 0;
      memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    }

    HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }
  // LPUART1使用阻塞模式，不需要中断处理
}

// Date validation structure for continuity checking
typedef struct {
    uint8_t last_valid_day;
    uint8_t last_valid_month;
    uint8_t last_valid_year;
    uint8_t last_valid_hour;
    uint32_t last_timestamp;
    uint8_t consecutive_count;
} DateValidation_t;

static DateValidation_t date_validator = {0};

// Global variables for backup date storage
static uint8_t backup_valid_day = 0;
static uint8_t backup_valid_month = 0;
static uint8_t backup_valid_year = 0;
static uint8_t backup_date_initialized = 0;

// Function to detect specific 040625 error
uint8_t detect_040625_error(uint8_t day, uint8_t month, uint8_t year)
{
    // Check for the specific 04/06/25 combination
    if (day == 4 && month == 6 && year == 25) {
        return 1; // 040625 error detected
    }
    return 0;
}

// Function to check if date is reasonable (not RTC initialization default)
uint8_t is_date_reasonable(uint8_t day, uint8_t month, uint8_t year)
{
    // Check if it's the RTC initialization default date (01/01/25)
    if (day == 1 && month == 1 && year == 25) {
        return 0; // This is likely RTC initialization default, not reasonable
    }

    // Check basic date validity
    if (year < 20 || year > 30 || month < 1 || month > 12 || day < 1 || day > 31) {
        return 0; // Invalid date format
    }

    return 1; // Date seems reasonable
}

// Function to validate date continuity
uint8_t validate_date_continuity(uint8_t day, uint8_t month, uint8_t year, uint8_t hour)
{
    uint32_t current_timestamp = HAL_GetTick();

    // First time initialization - only accept reasonable dates
    if (date_validator.consecutive_count == 0) {
        if (is_date_reasonable(day, month, year)) {
            date_validator.last_valid_day = day;
            date_validator.last_valid_month = month;
            date_validator.last_valid_year = year;
            date_validator.last_valid_hour = hour;
            date_validator.last_timestamp = current_timestamp;
            date_validator.consecutive_count = 1;
            printf("First reasonable date accepted: %02d/%02d/%02d\r\n", day, month, year);
            return 1; // Accept first reasonable date
        } else {
            printf("First date rejected as unreasonable: %02d/%02d/%02d\r\n", day, month, year);
            return 0; // Reject unreasonable first date
        }
    }

    // Calculate time difference in hours (approximate)
    uint32_t time_diff_ms = current_timestamp - date_validator.last_timestamp;
    uint32_t time_diff_hours = time_diff_ms / (1000 * 60 * 60);

    // Check for reasonable date progression
    int day_diff = day - date_validator.last_valid_day;
    int month_diff = month - date_validator.last_valid_month;
    int year_diff = year - date_validator.last_valid_year;

    // Handle month rollover
    if (day_diff < 0 && month_diff == 1) {
        day_diff += 31; // Approximate month rollover
    }

    // Check for midnight rollover (hour 23->0-2 with day+1)
    uint8_t is_midnight_rollover = 0;
    if ((date_validator.last_valid_hour >= 22) && (hour <= 2) && (day_diff == 1)) {
        is_midnight_rollover = 1;
    }

    // Validate date change is reasonable
    if (year_diff == 0 && month_diff == 0 && abs(day_diff) <= 1) {
        // Same month, day difference <= 1: reasonable
        return 1;
    } else if (year_diff == 0 && month_diff == 1 && day_diff <= 31 && day_diff >= -31) {
        // Month rollover: reasonable
        return 1;
    } else if (is_midnight_rollover) {
        // Midnight rollover: reasonable
        return 1;
    } else if (time_diff_hours > 48 && abs(day_diff) <= 3) {
        // Long time gap with small date change: might be reasonable
        return 1;
    }

    return 0; // Date change seems unreasonable
}

// Function to save valid date to global backup variables
void save_valid_date_to_backup(uint8_t day, uint8_t month, uint8_t year)
{
    backup_valid_day = day;
    backup_valid_month = month;
    backup_valid_year = year;
    backup_date_initialized = 1;

//    printf("Backup date saved: %02d/%02d/%02d\r\n", day, month, year);
}

// Function to restore date from global backup variables
uint8_t restore_date_from_backup(uint8_t *day, uint8_t *month, uint8_t *year)
{
    if (backup_date_initialized && backup_valid_year >= 20 &&
        backup_valid_month >= 1 && backup_valid_month <= 12 &&
        backup_valid_day >= 1 && backup_valid_day <= 31) {

        *day = backup_valid_day;
        *month = backup_valid_month;
        *year = backup_valid_year;

        printf("Date restored from backup: %02d/%02d/%02d\r\n", *day, *month, *year);
        return 1; // Valid backup date found
    }

    printf("No valid backup date available\r\n");
    return 0; // No valid backup date
}

// Create complete data string for transmission
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size)
{
    char time_str[20] = {0};
    char sn_buffer[32] = {0};
    int8_t signal_quality = -128;

    // 使用GSM模块的真实信号值和CCID号
    strcpy(sn_buffer, gsm_ccid);    // 使用GSM模块的CCID号
    signal_quality = gsm_signal_quality;  // 使用GSM模块的信号质量

    // Step 1: Get GPS and RTC original time/date
    uint8_t gps_time_valid = (gps_data.valid && gps_data.hour <= 23 &&
                             gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t gps_date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                             gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // Get RTC time as backup source
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    RTC_GetDateTime(&rtc_time, &rtc_date);

    // Step 2: Determine time/date source - GPS优先且无验证修正
    uint8_t final_day, final_month, final_year;
    uint8_t final_hour, final_minute, final_second;
    uint8_t date_source_gps = 0; // 0=RTC, 1=GPS

    // 关键修复：当GPS有效且有定位时，直接使用GPS原始UTC时间，不进行任何验证或修正
    if (gps_data.valid && gps_data.latitude > 0 && gps_data.longitude > 0) {
        if (gps_time_valid && gps_date_valid) {
            // 直接使用GPS提供的完整UTC时间和日期，无任何修正
            final_hour = gps_data.hour;
            final_minute = gps_data.minute;
            final_second = gps_data.second;
            final_day = gps_data.day;
            final_month = gps_data.month;
            final_year = gps_data.year % 100;
            date_source_gps = 1;
            printf("Using GPS UTC time directly: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                   final_hour, final_minute, final_second, final_day, final_month, final_year);
        } else if (gps_time_valid && !gps_date_valid) {
            // GPS时间有效但日期无效，使用GPS时间+RTC日期
            final_hour = gps_data.hour;
            final_minute = gps_data.minute;
            final_second = gps_data.second;
            final_day = rtc_date.Date;
            final_month = rtc_date.Month;
            final_year = rtc_date.Year;
            date_source_gps = 0;
            printf("Using GPS time + RTC date: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                   final_hour, final_minute, final_second, final_day, final_month, final_year);
        } else {
            // GPS有定位但时间无效，使用RTC时间
            final_hour = rtc_time.Hours;
            final_minute = rtc_time.Minutes;
            final_second = rtc_time.Seconds;
            final_day = rtc_date.Date;
            final_month = rtc_date.Month;
            final_year = rtc_date.Year;
            date_source_gps = 0;
            printf("GPS positioning valid but time invalid, using RTC: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                   final_hour, final_minute, final_second, final_day, final_month, final_year);
        }
    } else {
        // GPS无效或无定位，使用RTC时间和日期，并进行验证修正
        final_hour = rtc_time.Hours;
        final_minute = rtc_time.Minutes;
        final_second = rtc_time.Seconds;
        final_day = rtc_date.Date;
        final_month = rtc_date.Month;
        final_year = rtc_date.Year;
        date_source_gps = 0;
        printf("GPS invalid, using RTC time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
               final_hour, final_minute, final_second, final_day, final_month, final_year);

        // 只有在使用RTC时间时才进行日期验证和修正
        // Step 3: Execute 040625 specific error detection (仅RTC时间)
        if (detect_040625_error(final_day, final_month, final_year)) {
            printf("040625 error detected in RTC! Attempting recovery...\r\n");

            // Try to restore from backup
            if (restore_date_from_backup(&final_day, &final_month, &final_year)) {
                printf("Date restored from backup: %02d/%02d/%02d\r\n", final_day, final_month, final_year);
            } else {
                printf("No valid backup date found, using safe default date\r\n");
                // Use a safe default date if backup is also invalid
                final_day = 1;
                final_month = 1;
                final_year = 25; // 2025
            }
        }

        // Step 4: Execute date continuity validation (仅RTC时间)
        if (date_validator.consecutive_count > 0) {
            if (!validate_date_continuity(final_day, final_month, final_year, final_hour)) {
                printf("RTC date continuity validation failed! Current: %02d/%02d/%02d, attempting recovery...\r\n",
                       final_day, final_month, final_year);

                // Try to restore from backup
                if (restore_date_from_backup(&final_day, &final_month, &final_year)) {
                    printf("Date restored from backup: %02d/%02d/%02d\r\n", final_day, final_month, final_year);
                } else {
                    printf("No valid backup date found, using safe default date\r\n");
                    // Use a safe default date if backup is also invalid
                    final_day = 1;
                    final_month = 1;
                    final_year = 25; // 2025
                }
            }
        } else {
            // First time running - try to establish first valid date
            if (!validate_date_continuity(final_day, final_month, final_year, final_hour)) {
                // Keep using current date but don't save as backup
                // This allows the system to continue working while waiting for valid GPS
            }
        }

        // Step 5: Update backup only for RTC source after validation
        if ((date_validator.consecutive_count > 0 && validate_date_continuity(final_day, final_month, final_year, final_hour)) ||
            (date_validator.consecutive_count == 0 && is_date_reasonable(final_day, final_month, final_year))) {

            uint8_t should_save_backup = 0;

            if (backup_date_initialized) {
                // RTC source and we already have a backup - update it
                should_save_backup = 1;
                printf("RTC date validated, updating existing backup\r\n");
            } else if (is_date_reasonable(final_day, final_month, final_year)) {
                // RTC source, no backup yet, but date seems reasonable
                should_save_backup = 1;
                printf("RTC date seems reasonable, creating first backup\r\n");
            } else {
                // RTC source, no backup, and date seems unreasonable (likely initialization default)
                printf("RTC date unreasonable, not saving as backup: %02d/%02d/%02d\r\n",
                       final_day, final_month, final_year);
            }

            if (should_save_backup) {
                save_valid_date_to_backup(final_day, final_month, final_year);
            }

            // Update validator state
            date_validator.last_valid_day = final_day;
            date_validator.last_valid_month = final_month;
            date_validator.last_valid_year = final_year;
            date_validator.last_valid_hour = final_hour;
            date_validator.last_timestamp = HAL_GetTick();
            date_validator.consecutive_count++;
        }
    }

    // Step 6: 对于GPS源，也更新验证器状态以保持连续性（但不进行验证修正）
    if (date_source_gps == 1) {
        // GPS源直接更新验证器状态，不进行任何修正
        date_validator.last_valid_day = final_day;
        date_validator.last_valid_month = final_month;
        date_validator.last_valid_year = final_year;
        date_validator.last_valid_hour = final_hour;
        date_validator.last_timestamp = HAL_GetTick();
        date_validator.consecutive_count++;

        // GPS源也保存备份，用于RTC失效时的恢复
        save_valid_date_to_backup(final_day, final_month, final_year);
    }

    // Step 7: Continue with original data synthesis logic
    // Format the validated time string
    snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
            final_hour, final_minute, final_second,
            final_day, final_month, final_year);

    // GPS data or default values - 使用原始NMEA格式
    float longitude = gps_data.longitude;
    float latitude = gps_data.latitude;
    float altitude = gps_data.altitude;
    uint8_t fix_quality = gps_data.fix_quality;
    uint8_t satellites = gps_data.satellites;
    float speed = gps_data.speed;
    float hdop = gps_data.hdop;
    float pdop = gps_data.pdop;
    float course = gps_data.course;

    // 添加GPS状态调试输出
//    printf("GPS debug: valid=%d, quality=%d, satellites=%d, hdop=%.1f\r\n",
//           gps_data.valid, fix_quality, satellites, hdop);

    if (!gps_data.valid) {
        // Use default values when GPS is invalid
        longitude = 0.0;
        latitude = 0.0;
        altitude = 0.0;
        fix_quality = 0;
        satellites = 0;
        speed = 0.0;
        hdop = 99.9;
        pdop = 99.9;
        course = 0.0;
    } else {
        // GPS valid but check for missing precision data
        if (hdop <= 0.0) {
            hdop = 2.5;  // Use reasonable default when HDOP not available
        }
        if (pdop <= 0.0 || pdop >= 99.0) {
            pdop = 3.0;  // Use reasonable default when PDOP not available or invalid
        }
    }

    // Get MCU internal temperature
    extern float mcu_temp;

    // Create data content string
    char data_content[200];
    snprintf(data_content, sizeof(data_content),
            "S+%.5f+%.5f+%s+%.1f+%d+%d+%.1f+%.2f+%.1f+%.1f+%.1f+%.1f+%.1f+%.1f+%.2f+%d+%s+E",
            longitude,
            latitude,
            time_str,
            altitude,
            fix_quality,
            satellites,
            speed,
            pw,
            mcu_temp,  // 使用MCU内部温度替换三轴传感器温度
            hdop,
            pdop,
            attitude.roll,
            attitude.pitch,
            attitude.yaw,
            course,
            signal_quality,
            sn_buffer
            );

    // Build complete string with length header
    uint16_t data_length = strlen(data_content);
    if (data_length < 100) {
        snprintf(output_buffer, buffer_size, "HY0%d%s", data_length, data_content);
    } else {
        snprintf(output_buffer, buffer_size, "HY%d%s", data_length, data_content);
    }

    return HAL_OK;
}

// 打印数据字符串（替代GM20模块发送，用于测试基础功能）
void Print_Data_String(const char *data_string)
{
    uint16_t data_length = strlen(data_string);

//    printf("=== Data Output (Replacing GSM Send) ===\r\n");
//    printf("Data Length: %d bytes\r\n", data_length);
//    printf("Data Content: %s\r\n", data_string);
//    printf("=====================================\r\n");
}

// Parse GPS data from buffer
void GPS_ParseData(void)
{
    char *line_start = gps_buffer;
    char *line_end;

    // Find complete NMEA sentences
    while ((line_end = strchr(line_start, '\n')) != NULL) {
        *line_end = '\0';  // Temporarily terminate string

        // GPS原始数据输出控制
        #if GPS_DEBUG_ENABLE
        printf("GPS: %s\r\n", line_start);
        #endif

        // Parse NMEA sentence
        if (strlen(line_start) > 6) {
            GPS_ParseNMEA(line_start, &gps_data);
        }

        line_start = line_end + 1;
    }

    // Move remaining data to buffer start
    if (line_start < gps_buffer + gps_buffer_index) {
        uint16_t remaining = gps_buffer + gps_buffer_index - line_start;
        memmove(gps_buffer, line_start, remaining);
        gps_buffer_index = remaining;
        gps_buffer[gps_buffer_index] = '\0';
    } else {
        // No remaining data, clear buffer
        gps_buffer_index = 0;
        gps_buffer[0] = '\0';
    }
}

// 新增：测试GPS关键参数打印功能
void Test_GPS_KeyParameters(void)
{
    printf("\r\n=== GPS Key Parameters Test ===\r\n");

    // 检查GPS数据是否可用
    if (gps_data.valid) {
        printf("GPS data is valid, printing current parameters:\r\n");
        GPS_PrintKeyParameters(&gps_data);
    } else {
        printf("GPS data is currently invalid. Showing structure anyway:\r\n");
        GPS_PrintKeyParameters(&gps_data);

        printf("\r\nNote: To get valid GPS data:\r\n");
        printf("1. Ensure GPS antenna is connected\r\n");
        printf("2. Place device outdoors with clear sky view\r\n");
        printf("3. Wait for GPS fix (may take 2-5 minutes)\r\n");
        printf("4. Look for 'GPS precise fix: quality=1' message\r\n");
    }

    printf("=== Test Complete ===\r\n\r\n");
}

/* USER CODE END 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6)
  {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */

  /* USER CODE END Callback 1 */
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  // User can add his own implementation to report the HAL error return state
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  // User can add his own implementation to report the file name and line number
  // ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line)
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
